# RVC相机彩色图像和点云采集程序使用说明

## 概述

本程序基于RVC相机示例代码开发，提供了两个主要的Python脚本来采集彩色图像和点云数据：

1. **capture_color_and_pointcloud.py** - 完整的面向对象实现，功能丰富
2. **simple_capture.py** - 简化版本，易于使用和理解

## 功能特性

### 主要功能
- ✅ 自动检测和连接RVC相机
- ✅ 同时采集彩色图像和点云数据
- ✅ 保存多种格式的数据文件
- ✅ 支持单次和多次采集
- ✅ 完整的错误处理和资源清理
- ✅ 中文界面和详细的状态提示

### 支持的数据格式
- **彩色图像**: PNG格式
- **点云数据**: PLY格式（普通点云和彩色点云）
- **深度图**: TIFF格式

## 文件说明

### capture_color_and_pointcloud.py
这是一个完整的面向对象实现，包含以下特性：
- `RVCCameraCapture`类封装了所有相机操作
- 支持获取额外数据（如深度图）
- 完善的错误处理和资源管理
- 可扩展的架构设计

### simple_capture.py
这是一个简化的函数式实现，特点：
- 代码结构简单，易于理解
- 提供交互式界面
- 支持单次和多次采集模式
- 适合快速使用和学习

## 使用方法

### 环境要求
- Python 3.x
- PyRVC库（RVC相机SDK）
- OpenCV (cv2)
- NumPy

### 快速开始

#### 方法1：使用简化版本（推荐初学者）
```bash
cd camera/rev_Python
python simple_capture.py
```

程序会提示选择采集模式：
- 输入 `1` 进行单次采集
- 输入 `2` 进行多次采集

#### 方法2：使用完整版本
```bash
cd camera/rev_Python
python capture_color_and_pointcloud.py
```

### 程序运行流程

1. **系统初始化**
   - 初始化RVC系统
   - 扫描连接的相机设备

2. **设备连接**
   - 自动选择第一个可用设备
   - 显示设备信息（名称和序列号）
   - 打开相机连接

3. **数据采集**
   - 执行相机捕获操作
   - 获取彩色图像数据
   - 获取点云数据
   - 获取深度图数据（可选）

4. **数据保存**
   - 创建带时间戳的文件名
   - 保存彩色图像（PNG格式）
   - 保存点云数据（PLY格式）
   - 保存彩色点云（PLY格式）
   - 保存深度图（TIFF格式）

5. **资源清理**
   - 关闭相机连接
   - 释放相机资源
   - 关闭RVC系统

## 输出文件

程序会在`Data`目录下创建以下文件：

```
Data/
├── [设备序列号]/
│   ├── [时间戳]_color.png          # OpenCV保存的彩色图像
│   ├── [时间戳]_image.png          # RVC方法保存的图像
│   ├── [时间戳]_pointcloud.ply     # 普通点云数据
│   ├── [时间戳]_color_pointcloud.ply # 彩色点云数据
│   └── [时间戳]_depth.tiff         # 深度图数据
```

### 文件命名规则
- 时间戳格式：`YYYYMMDD_HHMMSS`
- 例如：`20241201_143025_color.png`

## 多次采集模式

simple_capture.py支持多次连续采集：

```python
# 采集3次，间隔2秒
capture_multiple_times(count=3, interval=2)
```

每次采集会创建独立的子目录：
```
Data/
├── capture_01/
├── capture_02/
└── capture_03/
```

## 错误处理

程序包含完善的错误处理机制：

- ❌ **未找到设备**: 检查相机连接和驱动
- ❌ **相机打开失败**: 检查设备是否被其他程序占用
- ❌ **采集失败**: 检查相机状态和参数设置
- ❌ **保存失败**: 检查磁盘空间和写入权限

## 自定义配置

### 修改保存目录
```python
# 在函数调用时指定
capture_color_and_pointcloud(save_dir="MyData")
```

### 修改点云单位
```python
# 在代码中修改单位（毫米/米）
pointmap.Save(path, RVC.PointMapUnitEnum.Meter)  # 米
pointmap.Save(path, RVC.PointMapUnitEnum.Millimeter)  # 毫米
```

### 选择不同的相机
```python
# 如果有多个设备，可以指定索引
find_and_connect_camera(device_index=1)  # 选择第二个设备
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'PyRVC'**
   - 解决：安装RVC相机SDK和Python绑定

2. **未找到设备**
   - 检查USB/网络连接
   - 确认相机驱动已正确安装
   - 检查设备是否被其他程序占用

3. **采集失败**
   - 检查相机参数设置
   - 确认相机处于正常工作状态
   - 查看错误信息获取详细原因

4. **文件保存失败**
   - 检查磁盘空间
   - 确认目录写入权限
   - 检查文件路径是否有效

### 调试建议

1. 运行程序时注意观察控制台输出
2. 检查生成的错误信息
3. 确认相机硬件连接正常
4. 验证RVC SDK安装是否完整

## 扩展开发

### 添加新功能
基于`RVCCameraCapture`类可以轻松扩展功能：

```python
# 添加参数设置
def set_exposure_time(self, exposure_time):
    if self.camera:
        self.camera.SetExposureTime(exposure_time)

# 添加ROI设置
def set_roi(self, x, y, width, height):
    if self.camera:
        roi = RVC.ROI()
        roi.x, roi.y = x, y
        roi.width, roi.height = width, height
        self.camera.SetROI(roi)
```

### 集成到其他项目
```python
from capture_color_and_pointcloud import RVCCameraCapture

# 在你的项目中使用
capture = RVCCameraCapture()
capture.initialize_system()
capture.find_and_connect_camera()
image, pointmap = capture.capture_data()
# ... 处理数据
capture.cleanup()
```

## 技术支持

如有问题，请参考：
1. RVC相机官方文档
2. PyRVC API文档
3. 示例代码目录中的其他例程

---

**注意**: 使用前请确保相机硬件连接正常，并已正确安装RVC SDK。
