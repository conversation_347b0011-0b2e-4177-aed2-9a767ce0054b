# Copyright (c) RVBUST, Inc - All rights reserved.

# This example is to demonstrate the usage of Cluster Filter and Reflection Filter. Cluster Filter is used in almost
# all scenarios. It is employed to remove small clusters of outliers. Reflection Filter is designed to eliminate large
# areas of point cloud noise caused by reflections, which are difficult to remove with Cluster Filter alone.

import PyRVC as RVC
import os
import numpy as np
import cv2
from Utils.Tools import *

def App():

    # Initialize RVC X system.
    RVC.SystemInit()

    # Choose RVC X Camera type (USB, GigE or All)
    opt = RVC.SystemListDeviceTypeEnum.All

    # Scan all RVC X GigE Camera devices.
    ret, devices = RVC.SystemListDevices(opt)

    #  Find whether any RVC X GigE Camera is connected or not.
    if len(devices) == 0:
        print("Can not find any RVC X Camera!")
        RVC.SystemShutdown()
        return 1

    # Create a RVC X Camera and choose use left side camera.
    x = RVC.X1.Create(devices[0], RVC.CameraID_Left)

    # Test RVC X Camera is valid or not.
    if x.IsValid() is not True:
        print("RVC X Camera is not valid!")
        RVC.X1.Destroy(x)
        RVC.SystemShutdown()
        return 1
    #PrintCaptureMode(devices[0])

    # Open RVC X Camera.
    ret1 = x.Open()

    # Test RVC X Camera is opened or not.
    if not ret1:
        print("open camera failed!")
        RVC.X1.Destroy(x)
        RVC.SystemShutdown()
        return 1
    
    # Print ExposureTime Range
    _, exp_range_min, exp_range_max = x.GetExposureTimeRange()
    print("ExposureTime Range:[{}, {}]".format(exp_range_min, exp_range_max))

    cap_opt = RVC.X1_CaptureOptions()
    ret,cap_opt = x.LoadCaptureOptionParameters()


    #  The Cluster Filter removes floating points and isolated clusters from the point cloud.
    #  The 'noise_removal_distance' parameter indicates the maximum distance between points that are considered to be
    #  in the same class. Points that are farther apart than this value will be classified into a new class.
    #  The 'noise_removal_point_number' parameter specifies that if the number of points within the same cluster is less
    #  than this value, they will be considered as outliers and removed.

    cap_opt.noise_removal_distance = 0.5  # unit: mm
    cap_opt.noise_removal_point_number = 40

    
    # 'reflection_filter_threshold' used to remove large-area erroneous point clouds caused by reflections.
    # The higher this value is set, the more points will be removed by the reflection filter.
    # Setting it too large may remove data from thin and pointy objects.
    # range:[0, 30]

    cap_opt.reflection_filter_threshold = 10

    # Capture a point map and a image.
    ret2 = x.Capture(cap_opt)

    # Create saving address of image and point map.
    save_address = "Data"
    TryCreateDir(save_address)

    if ret2 == True:
        print("RVC X Camera capture successed!")

        # Get image data and image size.
        img = x.GetImage()

        # Convert image to array and save it.
        img = np.array(img, copy=False)
        cv2.imwrite("Data/test.png", img)
        print("Save image successed!")

        # Save point map (m) to file.
        if x.GetPointMap().Save("Data/test.ply", RVC.PointMapUnitEnum.Meter):
            print("Save point map successed!")
        else:
            print("Save point map failed!")
    else:
        print("RVC X Camera capture failed!")
        print(RVC.GetLastErrorMessage())
        x.Close()
        RVC.X1.Destroy(x)
        RVC.SystemShutdown()
        return 1

    # Close RVC X Camera.
    x.Close()

    # Destroy RVC X Camera.
    RVC.X1.Destroy(x)

    # Shutdown RVC X System.
    RVC.SystemShutdown()

    return 0


if __name__ == "__main__":
    App()
