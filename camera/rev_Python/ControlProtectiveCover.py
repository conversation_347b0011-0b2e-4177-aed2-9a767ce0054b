import PyRVC as RVC
import numpy as np
import cv2
import os
from Utils.Tools import *
import time

if __name__ == "__main__":
    # Initialize RVC X system
    RVC.SystemInit()

    # Choose RVC X Camera type (USB, GigE or All)
    opt = RVC.SystemListDeviceTypeEnum.All

    # Scan all RVC X Camera devices
    ret, devices = RVC.SystemListDevices(opt)
    print("RVC X Camera devices number:", len(devices))

    # Find whether any RVC X Camera is connected or not
    if len(devices) == 0:
        print("Can not find any RVC X Camera!")
        RVC.SystemShutdown()
        exit(1)

    _, info = devices[0].GetDeviceInfo()
    if not info.support_protective_cover:
        print("The device does not support protective cover")
        RVC.SystemShutdown()
        exit(1)

    # Create a RVC X Camera
    x = RVC.X2.Create(devices[0])

    # Test RVC X Camera is valid or not
    if x.IsValid() == False:
        print("RVC X Camera is not valid!")
        RVC.X2.Destroy(x)
        RVC.SystemShutdown()
        exit(1)

    # Print Supported Capture_Mode
    # PrintCaptureMode(devices[0])

    # Open RVC X Camera
    ret1 = x.Open()

    # Test RVC X Camera is opened or not
    if x.IsOpen() == False:
        print("RVC X Camera is not opened!")
        RVC.X2.Destroy(x)
        RVC.SystemShutdown()
        exit(1)

    _, status = x.GetProtectiveCoverStatus()
    if status != RVC.ProtectiveCoverStatus.ProtectiveCoverStatus_Open:
        x.OpenProtectiveCover()
        print("ProtectiveCover is opening")

    print("ProtectiveCover is open")

    _, opts = x.LoadCaptureOptionParameters()
    ret = x.Capture(opts)
    if not ret:
        print("Capture failed")
        print(RVC.GetLastErrorMessage())
        
    x.CloseProtectiveCover()
        
    print("ProtectiveCover is closed")
    
    # Close RVC X Camera
    x.Close()

    # Destroy RVC X Camera
    RVC.X2.Destroy(x)

    # Shut Down RVC X System
    RVC.SystemShutdown()
