# Copyright (c) RVBUST, Inc - All rights reserved.
import PyRVC as RVC
import os
import numpy as np
import cv2
from Utils.Tools import *


def CodedCircleMarkerDetectOffline(folder, imageName):
    img = cv2.imread(folder + "/" + imageName)
    type = RVC.CodedCircleMarkerType()
    type.N = 15
    type.r1_to_r0_ratio = 4.0 / 1.5
    type.r2_to_r0_ratio = 6.0 / 1.5
    markers = RVC.DetectCodedCircleMarker(img, type)
    print("markerNums: ", len(markers))
    for i in range(len(markers)):
        center = (int(markers[i].x), int(markers[i].y))
        cv2.circle(img, center, 0, (0, 0, 255), -1)
        cv2.putText(img, str(markers[i].code), center,
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    cv2.namedWindow("image")
    cv2.moveWindow("image", 50, 25)
    newHeight = 900
    newWidth = int(img.shape[1] / img.shape[0] * newHeight)
    img = cv2.resize(img, (newWidth, newHeight))
    cv2.imshow("image", img)
    cv2.waitKey(0)


def CodedCircleMarkerDetectOnline():
    RVC.SystemInit()
    opt = RVC.SystemListDeviceTypeEnum.GigE
    ret, devices = RVC.SystemListDevices(opt)
    if len(devices) == 0:
        print("Can not find any RVC X GigE Camera!")
        RVC.SystemShutdown()
        return 1
    device = devices[0]
    # device = RVC.SystemFindDevice("G2GM620B068")
    x = RVC.X2.Create(device)
    if x.IsValid() == True:
        print("RVC X Camera is valid!")
    else:
        print("RVC X Camera is not valid!")
        RVC.X2.Destroy(x)
        RVC.SystemShutdown()
        return 1
    ret1 = x.Open()
    if ret1 and x.IsOpen() == True:
        print("RVC X Camera is opened!")
    else:
        print("RVC X Camera is not opened!")
        RVC.X2.Destroy(x)
        RVC.SystemShutdown()
        return 1

    # while not press space
    while cv2.waitKey(10) != 32:
        x.Capture2D(RVC.CameraID_Left)
        img = x.GetImage(RVC.CameraID_Left)
        type = RVC.CodedCircleMarkerType()
        # **** Notice: need to be changed according to the actual type. ****
        type.N = 15
        type.r1_to_r0_ratio = 4.0 / 1.5
        type.r2_to_r0_ratio = 6.0 / 1.5
        markers = RVC.DetectCodedCircleMarker(img, type)
        img = np.array(img, copy=False)
        print("markerNums: ", len(markers))
        imgShow = img
        # if gray image, convert to BGR image
        if len(img.shape) == 2:
            imgShow = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        for i in range(len(markers)):
            center = (int(markers[i].x), int(markers[i].y))
            cv2.circle(imgShow, center, 0, (0, 0, 255), -1)
            cv2.putText(imgShow, str(markers[i].code), center,
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        cv2.namedWindow("image")
        cv2.moveWindow("image", 50, 25)
        newHeight = 900
        newWidth = int(imgShow.shape[1] / imgShow.shape[0] * newHeight)
        imgShow = cv2.resize(imgShow, (newWidth, newHeight))
        cv2.imshow("image", imgShow)

    x.Close()
    RVC.X2.Destroy(x)
    RVC.SystemShutdown()
    return 0


if __name__ == "__main__":
    CodedCircleMarkerDetectOnline()
    exit(0)

    CodedCircleMarkerDetectOffline("D:/", "0.png")
    exit(0)
