# -*- coding: utf-8 -*-
"""
RVC相机简单采集程序
功能：快速采集彩色图像和点云数据
基于RVC示例代码简化而来
"""

import PyRVC as RVC
import os
import numpy as np
import cv2
import time
from Utils.Tools import TryCreateDir


def capture_color_and_pointcloud(save_dir="Data", index=01):
    """
    采集彩色图像和点云数据的简单函数
    
    Args:
        save_dir (str): 保存目录，默认为"Data"
        
    Returns:
        bool: 成功返回True，失败返回False
    """
    
    print("=" * 40)
    print("RVC相机数据采集开始")
    print("=" * 40)
    
    try:
        # 1. 初始化RVC系统
        RVC.SystemInit()
        print("✓ RVC系统初始化成功")
        
        # 2. 扫描设备
        ret, devices = RVC.SystemListDevices(RVC.SystemListDeviceTypeEnum.All)
        print(f"发现设备数量: {len(devices)}")
        
        if len(devices) == 0:
            print("✗ 未找到任何RVC相机设备!")
            RVC.SystemShutdown()
            return False
        
        # 3. 获取设备信息
        device = devices[0]
        ret, device_info = device.GetDeviceInfo()
        if ret:
            print(f"✓ 设备信息: {device_info.name}-{device_info.sn}")
        
        # 4. 创建并打开相机
        camera = RVC.X1.Create(device, RVC.CameraID_Left)
        
        if not camera.IsValid():
            print("✗ 相机创建失败!")
            RVC.SystemShutdown()
            return False
        
        ret = camera.Open()
        if not ret or not camera.IsOpen():
            print("✗ 相机打开失败!")
            RVC.X1.Destroy(camera)
            RVC.SystemShutdown()
            return False
        
        print("✓ 相机打开成功")
        
        # 5. 显示曝光时间范围
        _, exp_min, exp_max = camera.GetExposureTimeRange()
        print(f"曝光时间范围: [{exp_min}, {exp_max}]")
        
        # 6. 执行捕获
        print("开始采集数据...")
        ret = camera.Capture()
        
        if not ret:
            print("✗ 数据采集失败!")
            print(f"错误信息: {RVC.GetLastErrorMessage()}")
            camera.Close()
            RVC.X1.Destroy(camera)
            RVC.SystemShutdown()
            return False
        
        print("✓ 数据采集成功!")
        
        # 7. 获取数据
        image = camera.GetImage()
        pointmap = camera.GetPointMap()
        depth_map = camera.GetDepthMap()
        
        # 8. 创建保存目录
        TryCreateDir(save_dir)
        if device_info and device_info.sn:
            save_dir = os.path.join(save_dir, device_info.sn)
            TryCreateDir(save_dir)
        
        # 9. 生成时间戳文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        saved_files = []
        
        # 10. 保存彩色图像
        if image is not None:
            # 获取图像信息
            width = image.GetSize().cols
            height = image.GetSize().rows
            print(f"图像尺寸: {width} x {height}")
            
            if image.GetType() == RVC.ImageTypeEnum.Mono8:
                print("相机类型: 单色相机")
            else:
                print("相机类型: 彩色相机")
            
            # # 使用OpenCV保存
            # img_array = np.array(image, copy=False)
            # image_path = os.path.join(save_dir, f"{timestamp}_color.png")
            # cv2.imwrite(image_path, img_array)
            # print(f"✓ 彩色图像保存: {image_path}")
            # saved_files.append(image_path)
            
            # 使用RVC方法保存
            rvc_image_path = os.path.join(save_dir, f"{timestamp}_image_{index}.png")
            image.SaveImage(rvc_image_path)
            saved_files.append(rvc_image_path)
        
        # 11. 保存点云数据
        if pointmap is not None:
            # 保存普通点云
            pointcloud_path = os.path.join(save_dir, f"{timestamp}_pointcloud_{index}.ply")
            if pointmap.Save(pointcloud_path, RVC.PointMapUnitEnum.Millimeter, True):
                print(f"✓ 点云保存: {pointcloud_path}")
                saved_files.append(pointcloud_path)
            
            # # 保存彩色点云（如果有图像）
            # if image is not None:
            #     color_pointcloud_path = os.path.join(save_dir, f"{timestamp}_color_pointcloud.ply")
            #     if pointmap.SaveWithImage(color_pointcloud_path, image, RVC.PointMapUnitEnum.Millimeter, True):
            #         print(f"✓ 彩色点云保存: {color_pointcloud_path}")
            #         saved_files.append(color_pointcloud_path)
        
        # 12. 保存深度图
        if depth_map is not None:
            depth_path = os.path.join(save_dir, f"{timestamp}_depth_{index}.tiff")
            if depth_map.SaveDepthMap(depth_path, True):
                print(f"✓ 深度图保存: {depth_path}")
                saved_files.append(depth_path)
        
        # 13. 显示结果
        print("\n" + "=" * 30)
        print("保存的文件:")
        for i, file_path in enumerate(saved_files, 1):
            print(f"{i}. {file_path}")
        
        print(f"\n✓ 采集完成! 共保存 {len(saved_files)} 个文件")
        
        # 14. 清理资源
        camera.Close()
        RVC.X1.Destroy(camera)
        RVC.SystemShutdown()
        print("✓ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 程序执行失败: {e}")
        # 确保资源被清理
        try:
            if 'camera' in locals():
                camera.Close()
                RVC.X1.Destroy(camera)
            RVC.SystemShutdown()
        except:
            pass
        return False


def capture_multiple_times(count=3, interval=2, save_dir="Data"):
    """
    多次采集数据
    
    Args:
        count (int): 采集次数
        interval (int): 采集间隔（秒）
        save_dir (str): 保存目录
    """
    print(f"开始连续采集 {count} 次，间隔 {interval} 秒")
    
    success_count = 0
    for i in range(count):
        print(f"\n第 {i+1}/{count} 次采集:")
        
        # 为每次采集创建子目录
        current_save_dir = os.path.join(save_dir, f"capture_{i+1:02d}")
        
        if capture_color_and_pointcloud(current_save_dir):
            success_count += 1
            print(f"✓ 第 {i+1} 次采集成功")
        else:
            print(f"✗ 第 {i+1} 次采集失败")
        
        # 如果不是最后一次，等待间隔时间
        if i < count - 1:
            print(f"等待 {interval} 秒...")
            time.sleep(interval)
    
    print(f"\n采集完成: {success_count}/{count} 次成功")


def main():
    """主函数"""
    print("RVC相机采集程序")
    print("1. 单次采集")
    print("2. 多次采集")
    
    try:
        choice = input("请选择模式 (1/2): ").strip()
        
        if choice == "1":
            # 单次采集
            capture_color_and_pointcloud()
        elif choice == "2":
            # 多次采集
            try:
                count = int(input("请输入采集次数 (默认3): ") or "3")
                interval = int(input("请输入采集间隔秒数 (默认2): ") or "2")
                capture_multiple_times(count, interval)
            except ValueError:
                print("输入无效，使用默认参数")
                capture_multiple_times()
        else:
            print("无效选择，执行单次采集")
            capture_color_and_pointcloud()
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行失败: {e}")


if __name__ == "__main__":
    capture_color_and_pointcloud()
