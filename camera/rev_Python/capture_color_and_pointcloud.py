# -*- coding: utf-8 -*-
"""
RVC相机彩色图像和点云采集程序
功能：同时采集彩色图像和点云数据，并保存到文件
作者：基于RVC示例代码修改
"""

import PyRVC as RVC
import os
import numpy as np
import cv2
import time
from Utils.Tools import TryCreateDir


class RVCCameraCapture:
    """RVC相机采集类"""
    
    def __init__(self):
        """初始化相机采集器"""
        self.camera = None
        self.is_initialized = False
        self.device_info = None
        
    def initialize_system(self):
        """初始化RVC系统"""
        try:
            # 初始化RVC X系统
            RVC.SystemInit()
            print("✓ RVC系统初始化成功")
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"✗ RVC系统初始化失败: {e}")
            return False
    
    def find_and_connect_camera(self, device_index=0):
        """查找并连接相机"""
        try:
            # 扫描所有RVC X相机设备（USB、GigE或全部）
            opt = RVC.SystemListDeviceTypeEnum.All
            ret, devices = RVC.SystemListDevices(opt)
            
            print(f"发现RVC X相机设备数量: {len(devices)}")
            
            # 检查是否找到任何RVC X相机
            if len(devices) == 0:
                print("✗ 未找到任何RVC X相机!")
                return False
                
            # 获取设备信息
            device = devices[device_index]
            ret, self.device_info = device.GetDeviceInfo()
            if ret:
                print(f"✓ 找到设备: {self.device_info.name}-{self.device_info.sn}")
            
            # 创建RVC X相机实例（使用左侧相机）
            self.camera = RVC.X1.Create(device, RVC.CameraID_Left)
            
            # 验证相机是否有效
            if not self.camera.IsValid():
                print("✗ RVC X相机无效!")
                return False
            print("✓ RVC X相机创建成功")
            
            # 打开RVC X相机
            ret = self.camera.Open()
            if not ret or not self.camera.IsOpen():
                print("✗ RVC X相机打开失败!")
                return False
            print("✓ RVC X相机打开成功")
            
            # 打印曝光时间范围
            _, exp_min, exp_max = self.camera.GetExposureTimeRange()
            print(f"曝光时间范围: [{exp_min}, {exp_max}]")
            
            return True
            
        except Exception as e:
            print(f"✗ 相机连接失败: {e}")
            return False
    
    def capture_data(self):
        """采集彩色图像和点云数据"""
        try:
            if not self.camera or not self.camera.IsOpen():
                print("✗ 相机未正确初始化或打开")
                return None, None
            
            print("开始采集数据...")
            
            # 执行捕获
            ret = self.camera.Capture()
            if not ret:
                print("✗ 相机捕获失败!")
                print(f"错误信息: {RVC.GetLastErrorMessage()}")
                return None, None
            
            print("✓ 相机捕获成功!")
            
            # 获取彩色图像数据
            image = self.camera.GetImage()
            if image is None:
                print("✗ 获取图像数据失败")
                return None, None
            
            # 获取图像尺寸和类型信息
            width = image.GetSize().cols
            height = image.GetSize().rows
            print(f"图像尺寸: {width} x {height}")
            
            # 检查相机颜色信息
            if image.GetType() == RVC.ImageTypeEnum.Mono8:
                print("相机类型: 单色相机")
            else:
                print("相机类型: 彩色相机")
            
            # 获取点云数据
            pointmap = self.camera.GetPointMap()
            if pointmap is None:
                print("✗ 获取点云数据失败")
                return image, None
            
            print("✓ 成功获取彩色图像和点云数据")
            return image, pointmap
            
        except Exception as e:
            print(f"✗ 数据采集失败: {e}")
            return None, None
    
    def save_data(self, image, pointmap, save_dir="Data", filename_prefix=None):
        """保存彩色图像和点云数据"""
        try:
            # 创建保存目录
            TryCreateDir(save_dir)
            
            # 如果提供了设备信息，创建设备特定的子目录
            if self.device_info and self.device_info.sn:
                save_dir = os.path.join(save_dir, self.device_info.sn)
                TryCreateDir(save_dir)
            
            # 生成文件名前缀（包含时间戳）
            if filename_prefix is None:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename_prefix = f"capture_{timestamp}"
            
            saved_files = []
            
            # 保存彩色图像
            if image is not None:
                # 转换为numpy数组并保存
                img_array = np.array(image, copy=False)
                image_path = os.path.join(save_dir, f"{filename_prefix}_color.png")
                cv2.imwrite(image_path, img_array)
                print(f"✓ 彩色图像保存成功: {image_path}")
                saved_files.append(image_path)
                
                # 也使用RVC内置方法保存
                rvc_image_path = os.path.join(save_dir, f"{filename_prefix}_color_rvc.png")
                image.SaveImage(rvc_image_path)
                saved_files.append(rvc_image_path)
            
            # 保存点云数据
            if pointmap is not None:
                # 保存普通点云（PLY格式，毫米单位）
                pointcloud_path = os.path.join(save_dir, f"{filename_prefix}_pointcloud.ply")
                if pointmap.Save(pointcloud_path, RVC.PointMapUnitEnum.Millimeter, True):
                    print(f"✓ 点云数据保存成功: {pointcloud_path}")
                    saved_files.append(pointcloud_path)
                else:
                    print("✗ 点云数据保存失败")
                
                # 如果有彩色图像，保存带颜色的点云
                if image is not None:
                    color_pointcloud_path = os.path.join(save_dir, f"{filename_prefix}_color_pointcloud.ply")
                    if pointmap.SaveWithImage(color_pointcloud_path, image, RVC.PointMapUnitEnum.Millimeter, True):
                        print(f"✓ 彩色点云数据保存成功: {color_pointcloud_path}")
                        saved_files.append(color_pointcloud_path)
                    else:
                        print("✗ 彩色点云数据保存失败")
            
            return saved_files
            
        except Exception as e:
            print(f"✗ 数据保存失败: {e}")
            return []
    
    def get_additional_data(self):
        """获取额外的数据（深度图等）"""
        try:
            additional_data = {}
            
            if self.camera and self.camera.IsOpen():
                # 获取深度图
                depth_map = self.camera.GetDepthMap()
                if depth_map is not None:
                    additional_data['depth_map'] = depth_map
                    print("✓ 获取深度图成功")
                
            return additional_data
            
        except Exception as e:
            print(f"✗ 获取额外数据失败: {e}")
            return {}
    
    def save_additional_data(self, additional_data, save_dir="Data", filename_prefix=None):
        """保存额外数据"""
        try:
            if not additional_data:
                return []
            
            # 生成文件名前缀
            if filename_prefix is None:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename_prefix = f"capture_{timestamp}"
            
            saved_files = []
            
            # 保存深度图
            if 'depth_map' in additional_data:
                depth_path = os.path.join(save_dir, f"{filename_prefix}_depth.tiff")
                if additional_data['depth_map'].SaveDepthMap(depth_path, True):
                    print(f"✓ 深度图保存成功: {depth_path}")
                    saved_files.append(depth_path)
                else:
                    print("✗ 深度图保存失败")
            
            return saved_files
            
        except Exception as e:
            print(f"✗ 额外数据保存失败: {e}")
            return []
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.camera:
                if self.camera.IsOpen():
                    self.camera.Close()
                    print("✓ 相机已关闭")
                
                RVC.X1.Destroy(self.camera)
                print("✓ 相机资源已释放")
                self.camera = None
            
            if self.is_initialized:
                RVC.SystemShutdown()
                print("✓ RVC系统已关闭")
                self.is_initialized = False
                
        except Exception as e:
            print(f"✗ 资源清理失败: {e}")


def main():
    """主函数"""
    print("=" * 50)
    print("RVC相机彩色图像和点云采集程序")
    print("=" * 50)
    
    # 创建相机采集器
    capture = RVCCameraCapture()
    
    try:
        # 初始化系统
        if not capture.initialize_system():
            return -1
        
        # 查找并连接相机
        if not capture.find_and_connect_camera():
            return -1
        
        # 采集数据
        image, pointmap = capture.capture_data()
        if image is None and pointmap is None:
            print("✗ 数据采集失败")
            return -1
        
        # 保存主要数据
        saved_files = capture.save_data(image, pointmap)
        
        # 获取并保存额外数据
        additional_data = capture.get_additional_data()
        if additional_data:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            additional_files = capture.save_additional_data(
                additional_data, 
                save_dir="Data", 
                filename_prefix=f"capture_{timestamp}"
            )
            saved_files.extend(additional_files)
        
        # 显示保存的文件列表
        print("\n" + "=" * 30)
        print("保存的文件列表:")
        for i, file_path in enumerate(saved_files, 1):
            print(f"{i}. {file_path}")
        
        print(f"\n✓ 采集完成! 共保存 {len(saved_files)} 个文件")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return -1
    except Exception as e:
        print(f"\n✗ 程序执行失败: {e}")
        return -1
    finally:
        # 确保资源被正确清理
        capture.cleanup()


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
